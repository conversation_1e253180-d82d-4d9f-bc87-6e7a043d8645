use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Pixiv artwork type
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ArtworkType {
    #[serde(rename = "illust")]
    Illust,
    #[serde(rename = "manga")]
    Manga,
    #[serde(rename = "novel")]
    Novel,
}

impl std::fmt::Display for ArtworkType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ArtworkType::Illust => write!(f, "Illust"),
            ArtworkType::Manga => write!(f, "Manga"),
            ArtworkType::Novel => write!(f, "Novel"),
        }
    }
}

/// User information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct User {
    pub id: u64,
    pub name: String,
    pub account: String,
    #[serde(rename = "profileImageUrls")]
    pub profile_image_urls: HashMap<String, String>,
    pub comment: Option<String>,
    #[serde(rename = "isFollowed")]
    pub is_followed: bool,
}

/// Series information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Series {
    pub id: u64,
    pub title: String,
}

/// Tag information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tag {
    pub name: String,
    #[serde(rename = "translatedName")]
    pub translated_name: Option<String>,
}

/// Image URLs for different sizes
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageUrls {
    #[serde(rename = "squareMedium")]
    pub square_medium: Option<String>,
    pub medium: Option<String>,
    pub large: Option<String>,
    pub original: String,
}

/// Artwork metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Artwork {
    pub id: u64,
    pub title: String,
    #[serde(rename = "type")]
    pub artwork_type: ArtworkType,
    #[serde(rename = "imageUrls")]
    pub image_urls: Option<ImageUrls>,
    pub caption: String,
    pub restrict: u32,
    pub user: User,
    pub tags: Vec<Tag>,
    pub tools: Option<Vec<String>>,
    #[serde(rename = "createDate")]
    pub create_date: DateTime<Utc>,
    #[serde(rename = "uploadDate")]
    pub upload_date: DateTime<Utc>,
    #[serde(rename = "pageCount")]
    pub page_count: u32,
    pub width: Option<u32>,
    pub height: Option<u32>,
    #[serde(rename = "sanityLevel")]
    pub sanity_level: u32,
    #[serde(rename = "xRestrict")]
    pub x_restrict: u32,
    pub series: Option<serde_json::Value>,
    #[serde(rename = "metaSinglePage")]
    pub meta_single_page: Option<HashMap<String, serde_json::Value>>,
    #[serde(rename = "metaPages")]
    pub meta_pages: Option<Vec<HashMap<String, serde_json::Value>>>,
    #[serde(rename = "totalView")]
    pub total_view: u64,
    #[serde(rename = "totalBookmarks")]
    pub total_bookmarks: u64,
    #[serde(rename = "isBookmarked")]
    pub is_bookmarked: bool,
    pub visible: bool,
    #[serde(rename = "isMuted")]
    pub is_muted: bool,
    #[serde(rename = "totalComments")]
    pub total_comments: Option<u64>,
}

/// Novel-specific metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Novel {
    pub id: u64,
    pub title: String,
    pub caption: String,
    pub restrict: u32,
    #[serde(rename = "xRestrict")]
    pub x_restrict: u32,
    #[serde(rename = "isOriginal")]
    pub is_original: bool,
    #[serde(rename = "imageUrls")]
    pub image_urls: Option<HashMap<String, String>>,
    #[serde(rename = "createDate")]
    pub create_date: DateTime<Utc>,
    #[serde(rename = "uploadDate")]
    pub upload_date: DateTime<Utc>,
    pub tags: Vec<Tag>,
    #[serde(rename = "pageCount")]
    pub page_count: Option<u32>,
    #[serde(rename = "textLength")]
    pub text_length: u32,
    pub user: User,
    pub series: Option<serde_json::Value>,
    #[serde(rename = "isBookmarked")]
    pub is_bookmarked: bool,
    #[serde(rename = "totalBookmarks")]
    pub total_bookmarks: u64,
    #[serde(rename = "totalView")]
    pub total_view: u64,
    #[serde(rename = "totalComments")]
    pub total_comments: u64,
    #[serde(rename = "isMuted")]
    pub is_muted: bool,
    #[serde(rename = "isMyPixiv")]
    pub is_my_pixiv: bool,
    pub visible: bool,
    #[serde(rename = "coverImageUrls")]
    pub cover_image_urls: Option<HashMap<String, String>>,
    pub content: Option<String>, // Novel content when fetched
}

/// API response wrapper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub error: bool,
    pub message: String,
    pub body: Option<T>,
}

/// User profile response body (actual Pixiv API structure)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserProfileBody {
    #[serde(rename = "userId")]
    pub user_id: String,
    pub name: String,
    pub image: String,
    #[serde(rename = "imageBig")]
    pub image_big: String,
    pub premium: bool,
    #[serde(rename = "isFollowed")]
    pub is_followed: bool,
    #[serde(rename = "isMypixiv")]
    pub is_mypixiv: bool,
    #[serde(rename = "isBlocking")]
    pub is_blocking: bool,
    pub background: Option<serde_json::Value>,
    #[serde(rename = "sketchLiveId")]
    pub sketch_live_id: Option<serde_json::Value>,
    pub partial: Option<u32>,
    #[serde(rename = "acceptRequest")]
    pub accept_request: Option<bool>,
    #[serde(rename = "sketchLives")]
    pub sketch_lives: Option<Vec<serde_json::Value>>,
    // Additional fields for full profile
    pub following: Option<u32>,
    #[serde(rename = "followedBack")]
    pub followed_back: Option<bool>,
    pub comment: Option<String>,
    #[serde(rename = "commentHtml")]
    pub comment_html: Option<String>,
    pub webpage: Option<serde_json::Value>,
    pub social: Option<HashMap<String, serde_json::Value>>,
    #[serde(rename = "canSendMessage")]
    pub can_send_message: Option<bool>,
    pub region: Option<HashMap<String, serde_json::Value>>,
    pub age: Option<HashMap<String, serde_json::Value>>,
    #[serde(rename = "birthDay")]
    pub birth_day: Option<HashMap<String, serde_json::Value>>,
    pub gender: Option<HashMap<String, serde_json::Value>>,
    pub job: Option<HashMap<String, serde_json::Value>>,
    pub workspace: Option<HashMap<String, serde_json::Value>>,
    pub official: Option<bool>,
    pub group: Option<Vec<HashMap<String, serde_json::Value>>>,
}

impl UserProfileBody {
    /// Convert to User struct for compatibility
    pub fn to_user(&self) -> User {
        let mut profile_image_urls = HashMap::new();
        profile_image_urls.insert("medium".to_string(), self.image.clone());
        profile_image_urls.insert("large".to_string(), self.image_big.clone());

        User {
            id: self.user_id.parse().unwrap_or(0),
            name: self.name.clone(),
            account: self.name.clone(), // Pixiv API doesn't provide separate account field
            profile_image_urls,
            comment: self.comment.clone(),
            is_followed: self.is_followed,
        }
    }
}

/// User artworks response body (actual Pixiv API structure)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserArtworksBody {
    pub illusts: HashMap<String, Option<serde_json::Value>>,
    pub manga: HashMap<String, Option<serde_json::Value>>,
    pub novels: Vec<serde_json::Value>,
    #[serde(rename = "mangaSeries")]
    pub manga_series: Vec<serde_json::Value>,
    #[serde(rename = "novelSeries")]
    pub novel_series: Vec<serde_json::Value>,
    pub pickup: Vec<serde_json::Value>,
    #[serde(rename = "bookmarkCount")]
    pub bookmark_count: Option<HashMap<String, serde_json::Value>>,
    #[serde(rename = "externalSiteWorksStatus")]
    pub external_site_works_status: Option<HashMap<String, serde_json::Value>>,
    pub request: Option<HashMap<String, serde_json::Value>>,
}

impl UserArtworksBody {
    /// Get all artwork IDs (both illusts and manga)
    pub fn get_artwork_ids(&self) -> Vec<u64> {
        let mut ids = Vec::new();

        // Add illustration IDs
        for id_str in self.illusts.keys() {
            if let Ok(id) = id_str.parse::<u64>() {
                ids.push(id);
            }
        }

        // Add manga IDs
        for id_str in self.manga.keys() {
            if let Ok(id) = id_str.parse::<u64>() {
                ids.push(id);
            }
        }

        ids
    }

    /// Get illustration IDs only
    pub fn get_illust_ids(&self) -> Vec<u64> {
        self.illusts.keys()
            .filter_map(|id_str| id_str.parse::<u64>().ok())
            .collect()
    }

    /// Get manga IDs only
    pub fn get_manga_ids(&self) -> Vec<u64> {
        self.manga.keys()
            .filter_map(|id_str| id_str.parse::<u64>().ok())
            .collect()
    }
}

/// User novels response body
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserNovelsBody {
    pub works: Vec<Novel>,
    pub total: u32,
}

/// Artwork detail response body
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArtworkDetailBody {
    #[serde(rename = "illust")]
    pub artwork: Artwork,
}

/// Novel detail response body
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NovelDetailBody {
    pub novel: Novel,
}

/// Novel content response body
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NovelContentBody {
    #[serde(rename = "novelText")]
    pub novel_text: String,
    #[serde(rename = "novelMarker")]
    pub novel_marker: Option<HashMap<String, serde_json::Value>>,
}

/// Page detail for multi-page artworks
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageDetail {
    #[serde(rename = "imageUrls")]
    pub image_urls: ImageUrls,
    pub width: u32,
    pub height: u32,
}

/// Multi-page artwork response body
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArtworkPagesBody {
    pub pages: Vec<PageDetail>,
}

impl Artwork {
    /// Check if this artwork is R18
    pub fn is_r18(&self) -> bool {
        self.x_restrict > 0 || self.sanity_level >= 6
    }

    /// Get all tag names
    pub fn tag_names(&self) -> Vec<String> {
        self.tags.iter().map(|tag| tag.name.clone()).collect()
    }

    /// Check if artwork has any of the specified tags
    pub fn has_any_tag(&self, tags: &[String]) -> bool {
        let artwork_tags: Vec<String> = self.tag_names().iter().map(|t| t.to_lowercase()).collect();
        tags.iter()
            .any(|tag| artwork_tags.contains(&tag.to_lowercase()))
    }

    /// Check if artwork has all of the specified tags
    pub fn has_all_tags(&self, tags: &[String]) -> bool {
        let artwork_tags: Vec<String> = self.tag_names().iter().map(|t| t.to_lowercase()).collect();
        tags.iter()
            .all(|tag| artwork_tags.contains(&tag.to_lowercase()))
    }

    /// Get the series title or a default value
    pub fn series_title_or_default(&self, default: &str) -> String {
        self.series
            .as_ref()
            .and_then(|s| s.get("title"))
            .and_then(|t| t.as_str())
            .map(|t| t.to_string())
            .unwrap_or_else(|| default.to_string())
    }
}

impl Novel {
    /// Check if this novel is R18
    pub fn is_r18(&self) -> bool {
        self.x_restrict > 0
    }

    /// Get all tag names
    pub fn tag_names(&self) -> Vec<String> {
        self.tags.iter().map(|tag| tag.name.clone()).collect()
    }

    /// Check if novel has any of the specified tags
    pub fn has_any_tag(&self, tags: &[String]) -> bool {
        let novel_tags: Vec<String> = self.tag_names().iter().map(|t| t.to_lowercase()).collect();
        tags.iter()
            .any(|tag| novel_tags.contains(&tag.to_lowercase()))
    }

    /// Check if novel has all of the specified tags
    pub fn has_all_tags(&self, tags: &[String]) -> bool {
        let novel_tags: Vec<String> = self.tag_names().iter().map(|t| t.to_lowercase()).collect();
        tags.iter()
            .all(|tag| novel_tags.contains(&tag.to_lowercase()))
    }

    /// Get the series title or a default value
    pub fn series_title_or_default(&self, default: &str) -> String {
        self.series
            .as_ref()
            .and_then(|s| s.get("title"))
            .and_then(|t| t.as_str())
            .map(|t| t.to_string())
            .unwrap_or_else(|| default.to_string())
    }
}
