use crate::api::models::{Artwork, Novel};
use crate::utils::error::{<PERSON><PERSON><PERSON>, Result};
use crate::utils::helpers::{sanitize_filename, truncate_filename};
use chrono::{DateTime, Utc};
use regex::Regex;
use std::collections::HashMap;
use std::path::{Path, PathBuf};

/// Template variable resolver for generating file paths and names
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct PathTemplate {
    template: String,
    max_filename_length: usize,
    replacement_char: String,
}

impl PathTemplate {
    /// Create a new path template
    pub fn new(template: String, max_filename_length: usize, replacement_char: String) -> Self {
        Self {
            template,
            max_filename_length,
            replacement_char,
        }
    }

    /// Resolve template variables for an artwork
    pub fn resolve_for_artwork(
        &self,
        artwork: &Artwork,
        page_index: Option<usize>,
        extension: &str,
    ) -> Result<String> {
        let mut variables = HashMap::new();

        // Basic artwork information
        variables.insert("uid".to_string(), artwork.user.id.to_string());
        variables.insert(
            "username".to_string(),
            sanitize_filename(&artwork.user.name),
        );
        variables.insert("pid".to_string(), artwork.id.to_string());
        variables.insert("title".to_string(), sanitize_filename(&artwork.title));
        variables.insert("type".to_string(), artwork.artwork_type.to_string());
        variables.insert("ext".to_string(), extension.to_string());

        // Page information
        if let Some(page_idx) = page_index {
            variables.insert("page_index".to_string(), format!("p{:02}", page_idx));
        } else {
            variables.insert("page_index".to_string(), "p00".to_string());
        }
        variables.insert("page_count".to_string(), artwork.page_count.to_string());

        // Series information
        let series_title = artwork.series_title_or_default("_Unsorted");
        variables.insert("series_title".to_string(), sanitize_filename(&series_title));
        variables.insert(
            "series_title_or_No_Series".to_string(),
            sanitize_filename(&series_title),
        );
        if let Some(series) = &artwork.series {
            if let Some(id) = series.get("id").and_then(|i| i.as_u64()) {
                variables.insert("series_id".to_string(), id.to_string());
            } else {
                variables.insert("series_id".to_string(), "0".to_string());
            }
        } else {
            variables.insert("series_id".to_string(), "0".to_string());
        }

        // Date information
        self.add_date_variables(&mut variables, &artwork.upload_date);

        // Tags
        let tags = artwork.tag_names().join(&self.replacement_char);
        variables.insert("tags".to_string(), sanitize_filename(&tags));

        // R18 status
        variables.insert(
            "r18".to_string(),
            if artwork.is_r18() {
                "R18".to_string()
            } else {
                "".to_string()
            },
        );

        // Statistics
        variables.insert("like_count".to_string(), artwork.total_view.to_string());
        variables.insert(
            "bookmark_count".to_string(),
            artwork.total_bookmarks.to_string(),
        );

        self.resolve_template(&variables)
    }

    /// Resolve template variables for a novel
    pub fn resolve_for_novel(&self, novel: &Novel, extension: &str) -> Result<String> {
        let mut variables = HashMap::new();

        // Basic novel information
        variables.insert("uid".to_string(), novel.user.id.to_string());
        variables.insert("username".to_string(), sanitize_filename(&novel.user.name));
        variables.insert("pid".to_string(), novel.id.to_string());
        variables.insert("title".to_string(), sanitize_filename(&novel.title));
        variables.insert("type".to_string(), "Novel".to_string());
        variables.insert("ext".to_string(), extension.to_string());

        // Page information (novels don't have pages like artworks)
        variables.insert("page_index".to_string(), "".to_string());
        variables.insert(
            "page_count".to_string(),
            novel.page_count.unwrap_or(1).to_string(),
        );

        // Series information
        let series_title = novel.series_title_or_default("_Unsorted");
        variables.insert("series_title".to_string(), sanitize_filename(&series_title));
        variables.insert(
            "series_title_or_No_Series".to_string(),
            sanitize_filename(&series_title),
        );
        if let Some(series) = &novel.series {
            if let Some(id) = series.get("id").and_then(|i| i.as_u64()) {
                variables.insert("series_id".to_string(), id.to_string());
            } else {
                variables.insert("series_id".to_string(), "0".to_string());
            }
        } else {
            variables.insert("series_id".to_string(), "0".to_string());
        }

        // Date information
        self.add_date_variables(&mut variables, &novel.upload_date);

        // Tags
        let tags = novel.tag_names().join(&self.replacement_char);
        variables.insert("tags".to_string(), sanitize_filename(&tags));

        // R18 status
        variables.insert(
            "r18".to_string(),
            if novel.is_r18() {
                "R18".to_string()
            } else {
                "".to_string()
            },
        );

        // Statistics
        variables.insert("like_count".to_string(), novel.total_view.to_string());
        variables.insert(
            "bookmark_count".to_string(),
            novel.total_bookmarks.to_string(),
        );

        // Novel-specific
        variables.insert("word_count".to_string(), novel.text_length.to_string());

        self.resolve_template(&variables)
    }

    /// Add date-related variables to the variables map
    fn add_date_variables(&self, variables: &mut HashMap<String, String>, date: &DateTime<Utc>) {
        // Basic date formats
        variables.insert("upload_date".to_string(), date.format("%Y%m%d").to_string());

        // Support custom date formats like {upload_date:%Y-%m-%d}
        // This will be handled in the template resolution
    }

    /// Resolve the template with the given variables
    fn resolve_template(&self, variables: &HashMap<String, String>) -> Result<String> {
        let mut result = self.template.clone();

        // Handle date format patterns first (e.g., {upload_date:%Y-%m-%d})
        let date_pattern = Regex::new(r"\{upload_date:([^}]+)\}")
            .map_err(|e| Error::template(format!("Invalid date regex: {}", e)))?;

        if let Some(captures) = date_pattern.captures(&result) {
            let format_str = captures.get(1).unwrap().as_str();
            if let Some(date_str) = variables.get("upload_date") {
                // Parse the basic date and reformat it
                if let Ok(date) = chrono::NaiveDate::parse_from_str(date_str, "%Y%m%d") {
                    let formatted = date.format(format_str).to_string();
                    result = date_pattern.replace(&result, &formatted).to_string();
                }
            }
        }

        // Handle tag format patterns (e.g., {tags:_} or {tags: #})
        let tag_pattern = Regex::new(r"\{tags:([^}]+)\}")
            .map_err(|e| Error::template(format!("Invalid tag regex: {}", e)))?;

        if let Some(captures) = tag_pattern.captures(&result) {
            let separator = captures.get(1).unwrap().as_str();
            if let Some(tags_str) = variables.get("tags") {
                // Replace the default separator with the specified one
                let tags_with_separator = tags_str.replace(&self.replacement_char, separator);
                result = tag_pattern
                    .replace(&result, &tags_with_separator)
                    .to_string();
            }
        }

        // Handle regular variable substitution
        let var_pattern = Regex::new(r"\{([^}:]+)\}")
            .map_err(|e| Error::template(format!("Invalid variable regex: {}", e)))?;

        result = var_pattern
            .replace_all(&result, |caps: &regex::Captures| {
                let var_name = &caps[1];
                variables.get(var_name).cloned().unwrap_or_else(|| {
                    format!("{{{}}}", var_name) // Keep original if variable not found
                })
            })
            .to_string();

        // Sanitize and truncate the final result
        let sanitized = sanitize_filename(&result);
        let truncated = truncate_filename(&sanitized, self.max_filename_length);

        Ok(truncated)
    }

    /// Create a full path by combining base directory with resolved template
    pub fn create_full_path<P: AsRef<Path>>(&self, base_dir: P, resolved_path: &str) -> PathBuf {
        let base = base_dir.as_ref();
        let path = PathBuf::from(resolved_path);

        // If the resolved path is absolute, use it as-is
        if path.is_absolute() {
            path
        } else {
            base.join(path)
        }
    }

    /// Get the template string
    pub fn template(&self) -> &str {
        &self.template
    }

    /// Validate that the template contains valid variable names
    pub fn validate(&self) -> Result<()> {
        let var_pattern = Regex::new(r"\{([^}:]+)\}")
            .map_err(|e| Error::template(format!("Invalid variable regex: {}", e)))?;

        let valid_vars = [
            "uid",
            "username",
            "pid",
            "title",
            "type",
            "page_index",
            "page_count",
            "series_title",
            "series_id",
            "series_title_or_No_Series",
            "upload_date",
            "tags",
            "r18",
            "like_count",
            "bookmark_count",
            "ext",
            "word_count",
        ];

        for caps in var_pattern.captures_iter(&self.template) {
            let var_name = &caps[1];
            if !valid_vars.contains(&var_name)
                && !var_name.starts_with("upload_date:")
                && !var_name.starts_with("tags:")
            {
                return Err(Error::template(format!(
                    "Unknown template variable: {}",
                    var_name
                )));
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::api::models::{ArtworkType, Tag, User};
    use chrono::TimeZone;

    fn create_test_artwork() -> Artwork {
        Artwork {
            id: 12345,
            title: "Test Artwork".to_string(),
            artwork_type: ArtworkType::Illust,
            image_urls: None,
            caption: "Test caption".to_string(),
            restrict: 0,
            user: User {
                id: 67890,
                name: "Test User".to_string(),
                account: "testuser".to_string(),
                profile_image_urls: HashMap::new(),
                comment: None,
                is_followed: false,
            },
            tags: vec![
                Tag {
                    name: "tag1".to_string(),
                    translated_name: None,
                },
                Tag {
                    name: "tag2".to_string(),
                    translated_name: None,
                },
            ],
            tools: vec![],
            create_date: Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
            upload_date: Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
            page_count: 1,
            width: Some(1920),
            height: Some(1080),
            sanity_level: 2,
            x_restrict: 0,
            series: None,
            meta_single_page: None,
            meta_pages: None,
            total_view: 1000,
            total_bookmarks: 50,
            is_bookmarked: false,
            visible: true,
            is_muted: false,
            total_comments: Some(10),
        }
    }

    #[test]
    fn test_basic_template_resolution() {
        let template = PathTemplate::new(
            "{uid}_{username}_{pid}_{title}.{ext}".to_string(),
            200,
            "_".to_string(),
        );

        let artwork = create_test_artwork();
        let result = template
            .resolve_for_artwork(&artwork, Some(0), "jpg")
            .unwrap();

        assert_eq!(result, "67890_Test User_12345_Test Artwork.jpg");
    }

    #[test]
    fn test_date_format_template() {
        let template = PathTemplate::new(
            "{upload_date:%Y-%m-%d}_{pid}.{ext}".to_string(),
            200,
            "_".to_string(),
        );

        let artwork = create_test_artwork();
        let result = template.resolve_for_artwork(&artwork, None, "jpg").unwrap();

        assert_eq!(result, "2023-01-01_12345.jpg");
    }

    #[test]
    fn test_tag_format_template() {
        let template = PathTemplate::new("{pid}_{tags: #}.{ext}".to_string(), 200, "_".to_string());

        let artwork = create_test_artwork();
        let result = template.resolve_for_artwork(&artwork, None, "jpg").unwrap();

        assert_eq!(result, "12345_tag1 #tag2.jpg");
    }

    #[test]
    fn test_template_validation() {
        let valid_template = PathTemplate::new(
            "{uid}_{username}/{pid}.{ext}".to_string(),
            200,
            "_".to_string(),
        );
        assert!(valid_template.validate().is_ok());

        let invalid_template = PathTemplate::new(
            "{uid}_{invalid_var}/{pid}.{ext}".to_string(),
            200,
            "_".to_string(),
        );
        assert!(invalid_template.validate().is_err());
    }
}
